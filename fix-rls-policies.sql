np-- Fix RLS Policies for Assessment System
-- This script creates RLS policies that allow anonymous users to use the assessment system

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Allow anonymous users to create assessment sessions" ON assessment_sessions;
DROP POLICY IF EXISTS "Allow users to read their own assessment sessions" ON assessment_sessions;
DROP POLICY IF EXISTS "Allow users to update their own assessment sessions" ON assessment_sessions;
DROP POLICY IF EXISTS "Allow anonymous users to create assessment answers" ON assessment_answers;
DROP POLICY IF EXISTS "Allow users to read their own assessment answers" ON assessment_answers;
DROP POLICY IF EXISTS "Allow users to update their own assessment answers" ON assessment_answers;

-- Assessment Sessions Policies
-- Allow anonymous users to create sessions
CREATE POLICY "Allow anonymous users to create assessment sessions" ON assessment_sessions
    FOR INSERT 
    WITH CHECK (true);

-- Allow users to read their own sessions (including anonymous users)
CREATE POLICY "Allow users to read their own assessment sessions" ON assessment_sessions
    FOR SELECT 
    USING (true);

-- Allow users to update their own sessions
CREATE POLICY "Allow users to update their own assessment sessions" ON assessment_sessions
    FOR UPDATE 
    USING (true)
    WITH CHECK (true);

-- Assessment Answers Policies
-- Allow anonymous users to create answers
CREATE POLICY "Allow anonymous users to create assessment answers" ON assessment_answers
    FOR INSERT 
    WITH CHECK (true);

-- Allow users to read their own answers
CREATE POLICY "Allow users to read their own assessment answers" ON assessment_answers
    FOR SELECT 
    USING (true);

-- Allow users to update their own answers (for upsert operations)
CREATE POLICY "Allow users to update their own assessment answers" ON assessment_answers
    FOR UPDATE 
    USING (true)
    WITH CHECK (true);

-- Ensure RLS is enabled on both tables
ALTER TABLE assessment_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE assessment_answers ENABLE ROW LEVEL SECURITY;

-- Grant necessary permissions to anonymous users
GRANT USAGE ON SCHEMA public TO anon;
GRANT SELECT, INSERT, UPDATE ON assessment_sessions TO anon;
GRANT SELECT, INSERT, UPDATE ON assessment_answers TO anon;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO anon;

-- Optional: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_assessment_sessions_user_id ON assessment_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_assessment_sessions_instrument ON assessment_sessions(instrument);
CREATE INDEX IF NOT EXISTS idx_assessment_sessions_status ON assessment_sessions(status);
CREATE INDEX IF NOT EXISTS idx_assessment_answers_session_id ON assessment_answers(session_id);
CREATE INDEX IF NOT EXISTS idx_assessment_answers_question_id ON assessment_answers(question_id);

-- Verify the policies are created
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE tablename IN ('assessment_sessions', 'assessment_answers')
ORDER BY tablename, policyname;
