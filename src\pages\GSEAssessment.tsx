import { useState, useEffect } from "react";
import { Header } from "@/components/header";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useNavigate } from "react-router-dom";
import { CheckCircle, ArrowLeft, Home, Save } from "lucide-react";
import { useAssessmentSession } from "@/hooks/use-assessment-session";
import { useToast } from "@/hooks/use-toast";
import { DatabaseStatus } from "@/components/database-status";

const gseQuestions = [
  "Saya dapat menyelesaikan masalah sulit jika saya berusaha keras",
  "Jika seseorang menentang saya, saya dapat menemukan cara untuk mendapatkan apa yang saya inginkan",
  "Mudah bagi saya untuk tetap pada tujuan saya dan mencapai target",
  "Saya yakin bahwa saya dapat mengatasi kejadian tak terduga dengan efektif",
  "<PERSON><PERSON><PERSON> kecerdikan saya, saya tahu bagaimana menangani situasi yang tidak terduga",
  "Saya dapat menyelesaikan sebagian besar masalah jika saya menginvestasikan usaha yang diperlukan",
  "Saya dapat tetap tenang saat menghadapi kesulitan karena saya dapat mengandalkan kemampuan mengatasi masalah",
  "Ketika saya dihadapkan dengan masalah, saya biasanya dapat menemukan beberapa solusi",
  "Jika saya dalam kesulitan, saya biasanya dapat memikirkan sebuah solusi",
  "Saya biasanya dapat menangani apa pun yang menghadang saya"
];

const scaleLabels = [
  "Sangat Tidak Benar",
  "Tidak Benar", 
  "Benar",
  "Sangat Benar"
];

export default function GSEAssessment() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<number[]>([]);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  const [isCompleted, setIsCompleted] = useState(false);
  const [initializationError, setInitializationError] = useState<string | null>(null);
  const [isInitializing, setIsInitializing] = useState(true);
  const [showDatabaseStatus, setShowDatabaseStatus] = useState(false);
  const { session, progress: loadedProgress, createSession, saveAnswer, completeSession, isLoading, loadLatestSession } = useAssessmentSession('gse');

  useEffect(() => {
    const initialize = async () => {
      try {
        setIsInitializing(true);
        setInitializationError(null);

        const existingSession = await loadLatestSession();
        if (!existingSession) {
          const newSession = await createSession();
          if (!newSession) {
            throw new Error('Gagal membuat sesi assessment baru');
          }
        }
      } catch (error) {
        console.error('Assessment initialization error:', error);
        setInitializationError(error instanceof Error ? error.message : 'Gagal memuat assessment. Silakan coba lagi.');
        toast({
          title: "Kesalahan",
          description: "Gagal memuat assessment. Silakan coba lagi.",
          variant: "destructive"
        });
      } finally {
        setIsInitializing(false);
      }
    };
    initialize();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (session && loadedProgress && Object.keys(loadedProgress).length > 0) {
        const loadedAnswers: number[] = [];
        let lastQuestionAnswered = -1;

        for (let i = 0; i < gseQuestions.length; i++) {
            const questionKey = `gse_q${i + 1}`;
            if (loadedProgress[questionKey] !== undefined) {
                const answerValue = parseInt(loadedProgress[questionKey], 10);
                if (!isNaN(answerValue)) {
                  loadedAnswers[i] = answerValue;
                  lastQuestionAnswered = i;
                }
            }
        }
        setAnswers(loadedAnswers);

        const nextQuestion = lastQuestionAnswered < gseQuestions.length - 1
          ? lastQuestionAnswered + 1
          : gseQuestions.length - 1;

        setCurrentQuestion(nextQuestion);
        setSelectedAnswer(loadedAnswers[nextQuestion] ?? null);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loadedProgress, session]);

  const progress = ((currentQuestion + (selectedAnswer !== null ? 1 : 0)) / gseQuestions.length) * 100;

  const handleAnswer = (value: number) => {
    setSelectedAnswer(value);
  };

  const handleNext = async () => {
    if (selectedAnswer === null || !session) return;

    const newAnswers = [...answers];
    newAnswers[currentQuestion] = selectedAnswer;
    setAnswers(newAnswers);

    // Save answer to database
    await saveAnswer(`gse_q${currentQuestion + 1}`, selectedAnswer);
    
    setSelectedAnswer(null);

    if (currentQuestion < gseQuestions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
    } else {
      const result = calculateResult(newAnswers);
      await completeSession(result.totalScore, {
        category: result.category
      });
      setIsCompleted(true);
    }
  };

  const handleSaveProgress = async () => {
    if (selectedAnswer !== null && session) {
      const newAnswers = [...answers];
      newAnswers[currentQuestion] = selectedAnswer;
      setAnswers(newAnswers);
      await saveAnswer(`gse_q${currentQuestion + 1}`, selectedAnswer);
      
      toast({
        title: "Progress Tersimpan",
        description: "Jawaban Anda telah disimpan.",
      });
    }
  };

  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1);
      setSelectedAnswer(answers[currentQuestion - 1] || null);
    }
  };

  const calculateResult = (answersArray = answers) => {
    const totalScore = answersArray.reduce((sum, answer) => sum + (answer + 1), 0);
    let category = "";
    
    if (totalScore <= 25) category = "Rendah";
    else if (totalScore <= 30) category = "Sedang";
    else category = "Tinggi";

    return { totalScore, category };
  };

  // Show loading state during initialization
  if (isInitializing) {
    return (
      <div className="min-h-screen bg-gradient-soft">
        <Header
          title="GSE Assessment"
          subtitle="Memuat assessment..."
        />

        <div className="p-4 max-w-md mx-auto space-y-6">
          <Card className="p-6 shadow-medium border-0 bg-gradient-card animate-fade-in text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Memuat assessment...</p>
          </Card>
        </div>
      </div>
    );
  }

  // Show error state if initialization failed
  if (initializationError) {
    return (
      <div className="min-h-screen bg-gradient-soft">
        <Header
          title="GSE Assessment"
          subtitle="Kesalahan"
        />

        <div className="p-4 max-w-md mx-auto space-y-6">
          <Card className="p-6 shadow-medium border-0 bg-gradient-card animate-fade-in text-center">
            <div className="p-4 rounded-full bg-red-100 w-20 h-20 flex items-center justify-center mx-auto mb-4">
              <svg className="h-10 w-10 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>

            <h2 className="font-display font-bold text-xl text-foreground mb-2">
              Kesalahan
            </h2>
            <p className="text-muted-foreground mb-6">
              {initializationError}
            </p>

            <div className="space-y-3">
              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={() => navigate('/assessment')}
                  className="flex-1"
                >
                  Kembali
                </Button>
                <Button
                  onClick={() => window.location.reload()}
                  className="flex-1 bg-gradient-primary"
                >
                  Coba Lagi
                </Button>
              </div>

              <Button
                variant="outline"
                onClick={() => setShowDatabaseStatus(true)}
                className="w-full"
              >
                Periksa Koneksi Database
              </Button>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  if (isCompleted) {
    const result = calculateResult();

    return (
      <div className="min-h-screen bg-gradient-soft">
        <Header
          title="Hasil Assessment"
          subtitle="General Self-Efficacy Scale"
        />

        <div className="p-4 max-w-md mx-auto space-y-6">
          <Card className="p-6 shadow-medium border-0 bg-gradient-card animate-fade-in text-center">
            <div className="p-4 rounded-full bg-gradient-primary w-20 h-20 flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="h-10 w-10 text-white" />
            </div>

            <h2 className="font-display font-bold text-2xl text-foreground mb-2">
              Assessment Selesai!
            </h2>
            <p className="text-muted-foreground mb-6">
              Terima kasih telah menyelesaikan GSE Assessment
            </p>

            <div className="space-y-4">
              <div className="p-4 rounded-lg bg-primary-soft">
                <div className="text-3xl font-bold text-primary mb-1">
                  {result.totalScore}/40
                </div>
                <div className="text-sm text-muted-foreground">Skor Total</div>
              </div>

              <div className="p-4 rounded-lg bg-secondary">
                <div className="text-lg font-semibold text-foreground mb-1">
                  Kategori: {result.category}
                </div>
                <div className="text-sm text-muted-foreground">
                  {result.category === "Tinggi" && "Anda memiliki keyakinan diri yang sangat baik dalam mengatasi tantangan"}
                  {result.category === "Sedang" && "Anda memiliki keyakinan diri yang cukup baik"}
                  {result.category === "Rendah" && "Perlu meningkatkan keyakinan diri dalam menghadapi tantangan"}
                </div>
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <Button
                variant="outline"
                onClick={() => navigate('/')}
                className="flex-1"
              >
                Kembali ke Beranda
              </Button>
              <Button
                onClick={() => navigate('/assessment')}
                className="flex-1 bg-gradient-primary"
              >
                Assessment Lain
              </Button>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-soft">
      <Header 
        title="GSE Assessment" 
        subtitle={`Pertanyaan ${currentQuestion + 1} dari ${gseQuestions.length}`}
      />
      
      <div className="p-4 max-w-md mx-auto space-y-6">
        {/* Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>Progress</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Question Card */}
        <Card className="p-6 shadow-medium border-0 bg-gradient-card animate-fade-in">
          <h2 className="font-display font-semibold text-lg text-foreground mb-6 leading-relaxed">
            {gseQuestions[currentQuestion]}
          </h2>

          <div className="space-y-3">
            {scaleLabels.map((label, index) => (
              <button
                key={index}
                onClick={() => handleAnswer(index)}
                className={`w-full p-4 rounded-lg border-2 transition-all duration-200 text-left ${
                  selectedAnswer === index
                    ? 'border-primary bg-primary-soft shadow-soft'
                    : 'border-border hover:border-primary/50 hover:bg-muted/50'
                }`}
              >
                <div className="flex items-center gap-3">
                  <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                    selectedAnswer === index 
                      ? 'border-primary bg-primary' 
                      : 'border-muted-foreground'
                  }`}>
                    {selectedAnswer === index && (
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    )}
                  </div>
                  <span className="font-medium text-foreground">{label}</span>
                </div>
              </button>
            ))}
          </div>
        </Card>

        {/* Navigation */}
        <div className="space-y-3">
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentQuestion === 0}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Sebelumnya
            </Button>
            
            <Button
              onClick={handleNext}
              disabled={selectedAnswer === null || isLoading}
              className="flex-1 bg-gradient-primary"
            >
              {currentQuestion === gseQuestions.length - 1 ? 'Selesai' : 'Selanjutnya'}
            </Button>
          </div>

          {/* Progress controls */}
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => navigate('/')}
              className="flex items-center gap-2"
            >
              <Home className="h-4 w-4" />
              Beranda
            </Button>
            
            <Button
              variant="outline"
              onClick={handleSaveProgress}
              disabled={selectedAnswer === null || isLoading}
              className="flex-1 flex items-center gap-2"
            >
              <Save className="h-4 w-4" />
              Simpan Progress
            </Button>
          </div>
        </div>
      </div>

      {showDatabaseStatus && (
        <DatabaseStatus onClose={() => setShowDatabaseStatus(false)} />
      )}
    </div>
  );
}