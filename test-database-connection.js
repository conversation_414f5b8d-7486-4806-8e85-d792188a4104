#!/usr/bin/env node

/**
 * Database Connection Test Script
 * This script tests the Supabase database connection and basic operations
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.VITE_SUPABASE_ANON_KEY;

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

console.log(`${colors.blue}🔍 Database Connection Test${colors.reset}`);
console.log('===============================\n');

// Check environment variables
if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  console.error(`${colors.red}❌ Missing environment variables!${colors.reset}`);
  console.log(`VITE_SUPABASE_URL: ${SUPABASE_URL ? '✅ Set' : '❌ Missing'}`);
  console.log(`VITE_SUPABASE_ANON_KEY: ${SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing'}`);
  process.exit(1);
}

console.log(`${colors.green}✅ Environment variables loaded${colors.reset}`);
console.log(`URL: ${SUPABASE_URL}`);
console.log(`Key: ${SUPABASE_ANON_KEY.substring(0, 20)}...`);
console.log('');

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
  auth: {
    storage: {
      getItem: (key) => null,
      setItem: (key, value) => {},
      removeItem: (key) => {}
    },
    persistSession: false,
    autoRefreshToken: false,
  }
});

async function testDatabaseConnection() {
  console.log(`${colors.yellow}🔄 Testing database connection...${colors.reset}`);
  
  try {
    // Test basic connection
    const { data, error } = await supabase
      .from('assessment_sessions')
      .select('id')
      .limit(1);

    if (error) {
      console.error(`${colors.red}❌ Database connection failed:${colors.reset}`);
      console.error(`   Error code: ${error.code}`);
      console.error(`   Error message: ${error.message}`);
      
      if (error.message.includes('row-level security')) {
        console.log(`${colors.yellow}⚠️  This might be due to RLS policies (which is normal)${colors.reset}`);
        return testWithInsert();
      }
      return false;
    }

    console.log(`${colors.green}✅ Database connection successful${colors.reset}`);
    console.log(`   Retrieved ${data ? data.length : 0} records`);
    return true;

  } catch (error) {
    console.error(`${colors.red}❌ Connection test failed:${colors.reset}`);
    console.error(`   ${error.message}`);
    return false;
  }
}

async function testWithInsert() {
  console.log(`${colors.yellow}🔄 Testing with session creation...${colors.reset}`);
  
  try {
    const testUserId = 'test-user-' + Date.now();
    
    const { data, error } = await supabase
      .from('assessment_sessions')
      .insert({
        instrument: 'test',
        status: 'in_progress',
        user_id: testUserId,
        started_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      console.error(`${colors.red}❌ Insert test failed:${colors.reset}`);
      console.error(`   Error code: ${error.code}`);
      console.error(`   Error message: ${error.message}`);
      
      if (error.message.includes('row-level security')) {
        console.log(`${colors.green}✅ RLS is working (this is expected)${colors.reset}`);
        console.log(`   The database is accessible but protected by security policies`);
        return true;
      }
      return false;
    }

    console.log(`${colors.green}✅ Insert test successful${colors.reset}`);
    console.log(`   Created session with ID: ${data.id}`);
    
    // Clean up test data
    await supabase
      .from('assessment_sessions')
      .delete()
      .eq('id', data.id);
    
    return true;

  } catch (error) {
    console.error(`${colors.red}❌ Insert test failed:${colors.reset}`);
    console.error(`   ${error.message}`);
    return false;
  }
}

async function testUserIdGeneration() {
  console.log(`${colors.yellow}🔄 Testing user ID generation...${colors.reset}`);
  
  // Simulate the UUID generation from the hook
  const generateUUID = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  };

  const testUUID = generateUUID();
  console.log(`${colors.green}✅ Generated UUID: ${testUUID}${colors.reset}`);
  
  // Validate UUID format
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  const isValid = uuidRegex.test(testUUID);
  
  if (isValid) {
    console.log(`${colors.green}✅ UUID format is valid${colors.reset}`);
  } else {
    console.log(`${colors.red}❌ UUID format is invalid${colors.reset}`);
  }
  
  return isValid;
}

// Run all tests
async function runAllTests() {
  const results = [];
  
  results.push(await testDatabaseConnection());
  results.push(await testUserIdGeneration());
  
  console.log('\n' + '='.repeat(40));
  console.log(`${colors.blue}📊 Test Results Summary${colors.reset}`);
  console.log('='.repeat(40));
  
  const passed = results.filter(r => r).length;
  const total = results.length;
  
  if (passed === total) {
    console.log(`${colors.green}✅ All tests passed (${passed}/${total})${colors.reset}`);
    console.log(`${colors.green}🎉 Database connection is working properly!${colors.reset}`);
  } else {
    console.log(`${colors.red}❌ Some tests failed (${passed}/${total})${colors.reset}`);
    console.log(`${colors.yellow}⚠️  Please check the error messages above${colors.reset}`);
  }
}

runAllTests().catch(console.error);
