-- Supabase Database Setup for Assessment System
-- This file contains the necessary RLS policies to allow assessment functionality

-- Enable RLS on tables (if not already enabled)
ALTER TABLE assessment_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE assessment_answers ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Allow anonymous users to create assessment sessions" ON assessment_sessions;
DROP POLICY IF EXISTS "Allow users to read their own assessment sessions" ON assessment_sessions;
DROP POLICY IF EXISTS "Allow users to update their own assessment sessions" ON assessment_sessions;
DROP POLICY IF EXISTS "Allow anonymous users to create assessment answers" ON assessment_answers;
DROP POLICY IF EXISTS "Allow users to read their own assessment answers" ON assessment_answers;
DROP POLICY IF EXISTS "Allow users to update their own assessment answers" ON assessment_answers;

-- Assessment Sessions Policies
-- Allow anonymous users to create assessment sessions
CREATE POLICY "Allow anonymous users to create assessment sessions" 
ON assessment_sessions FOR INSERT 
TO anon 
WITH CHECK (true);

-- Allow users to read their own assessment sessions
CREATE POLICY "Allow users to read their own assessment sessions" 
ON assessment_sessions FOR SELECT 
TO anon 
USING (true);

-- Allow users to update their own assessment sessions
CREATE POLICY "Allow users to update their own assessment sessions" 
ON assessment_sessions FOR UPDATE 
TO anon 
USING (true)
WITH CHECK (true);

-- Assessment Answers Policies
-- Allow anonymous users to create assessment answers
CREATE POLICY "Allow anonymous users to create assessment answers" 
ON assessment_answers FOR INSERT 
TO anon 
WITH CHECK (true);

-- Allow users to read their own assessment answers
CREATE POLICY "Allow users to read their own assessment answers" 
ON assessment_answers FOR SELECT 
TO anon 
USING (true);

-- Allow users to update their own assessment answers
CREATE POLICY "Allow users to update their own assessment answers" 
ON assessment_answers FOR UPDATE 
TO anon 
USING (true)
WITH CHECK (true);

-- For authenticated users, add more restrictive policies
-- Allow authenticated users to create assessment sessions
CREATE POLICY "Allow authenticated users to create assessment sessions" 
ON assessment_sessions FOR INSERT 
TO authenticated 
WITH CHECK (auth.uid()::text = user_id);

-- Allow authenticated users to read their own assessment sessions
CREATE POLICY "Allow authenticated users to read their own assessment sessions" 
ON assessment_sessions FOR SELECT 
TO authenticated 
USING (auth.uid()::text = user_id);

-- Allow authenticated users to update their own assessment sessions
CREATE POLICY "Allow authenticated users to update their own assessment sessions" 
ON assessment_sessions FOR UPDATE 
TO authenticated 
USING (auth.uid()::text = user_id)
WITH CHECK (auth.uid()::text = user_id);

-- Allow authenticated users to create assessment answers for their sessions
CREATE POLICY "Allow authenticated users to create assessment answers" 
ON assessment_answers FOR INSERT 
TO authenticated 
WITH CHECK (
  EXISTS (
    SELECT 1 FROM assessment_sessions 
    WHERE id = session_id 
    AND user_id = auth.uid()::text
  )
);

-- Allow authenticated users to read their own assessment answers
CREATE POLICY "Allow authenticated users to read their own assessment answers" 
ON assessment_answers FOR SELECT 
TO authenticated 
USING (
  EXISTS (
    SELECT 1 FROM assessment_sessions 
    WHERE id = session_id 
    AND user_id = auth.uid()::text
  )
);

-- Allow authenticated users to update their own assessment answers
CREATE POLICY "Allow authenticated users to update their own assessment answers" 
ON assessment_answers FOR UPDATE 
TO authenticated 
USING (
  EXISTS (
    SELECT 1 FROM assessment_sessions 
    WHERE id = session_id 
    AND user_id = auth.uid()::text
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM assessment_sessions 
    WHERE id = session_id 
    AND user_id = auth.uid()::text
  )
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_assessment_sessions_user_id ON assessment_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_assessment_sessions_instrument ON assessment_sessions(instrument);
CREATE INDEX IF NOT EXISTS idx_assessment_sessions_status ON assessment_sessions(status);
CREATE INDEX IF NOT EXISTS idx_assessment_answers_session_id ON assessment_answers(session_id);
CREATE INDEX IF NOT EXISTS idx_assessment_answers_question_id ON assessment_answers(question_id);

-- Grant necessary permissions
GRANT ALL ON assessment_sessions TO anon;
GRANT ALL ON assessment_answers TO anon;
GRANT ALL ON assessment_sessions TO authenticated;
GRANT ALL ON assessment_answers TO authenticated;
