import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface DatabaseStatusProps {
  onClose?: () => void;
}

export function DatabaseStatus({ onClose }: DatabaseStatusProps) {
  const [status, setStatus] = useState<'testing' | 'success' | 'error'>('testing');
  const [message, setMessage] = useState('Testing database connection...');
  const [details, setDetails] = useState<string[]>([]);

  useEffect(() => {
    testConnection();
  }, []);

  const testConnection = async () => {
    setStatus('testing');
    setMessage('Testing database connection...');
    setDetails([]);

    try {
      // Test 1: Basic connection
      setDetails(prev => [...prev, '🔄 Testing basic connection...']);
      
      const { data, error } = await supabase
        .from('assessment_sessions')
        .select('id')
        .limit(1);

      if (error) {
        if (error.message.includes('row-level security')) {
          setDetails(prev => [...prev, '✅ Database accessible (RLS active)']);
        } else {
          throw new Error(`Connection failed: ${error.message}`);
        }
      } else {
        setDetails(prev => [...prev, `✅ Connection successful (${data?.length || 0} records)`]);
      }

      // Test 2: Environment variables
      setDetails(prev => [...prev, '🔄 Checking environment variables...']);
      const url = import.meta.env.VITE_SUPABASE_URL;
      const key = import.meta.env.VITE_SUPABASE_ANON_KEY;
      
      if (!url || !key) {
        throw new Error('Missing environment variables');
      }
      
      setDetails(prev => [...prev, '✅ Environment variables loaded']);
      setDetails(prev => [...prev, `   URL: ${url}`]);
      setDetails(prev => [...prev, `   Key: ${key.substring(0, 20)}...`]);

      // Test 3: User ID generation
      setDetails(prev => [...prev, '🔄 Testing user ID generation...']);
      const generateUUID = () => {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
          const r = Math.random() * 16 | 0;
          const v = c == 'x' ? r : (r & 0x3 | 0x8);
          return v.toString(16);
        });
      };

      const testUUID = generateUUID();
      setDetails(prev => [...prev, `✅ Generated UUID: ${testUUID}`]);

      // Test 4: Try creating a test session
      setDetails(prev => [...prev, '🔄 Testing session creation...']);
      
      const { data: sessionData, error: sessionError } = await supabase
        .from('assessment_sessions')
        .insert({
          instrument: 'test',
          status: 'in_progress',
          user_id: testUUID,
          started_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (sessionError) {
        if (sessionError.message.includes('row-level security')) {
          setDetails(prev => [...prev, '✅ RLS policies are active (expected)']);
        } else {
          setDetails(prev => [...prev, `⚠️ Session creation failed: ${sessionError.message}`]);
        }
      } else {
        setDetails(prev => [...prev, `✅ Session created: ${sessionData.id}`]);
        // Clean up
        await supabase.from('assessment_sessions').delete().eq('id', sessionData.id);
        setDetails(prev => [...prev, '✅ Test session cleaned up']);
      }

      setStatus('success');
      setMessage('Database connection is working properly!');

    } catch (error) {
      console.error('Database test error:', error);
      setStatus('error');
      setMessage(error instanceof Error ? error.message : 'Unknown error occurred');
      setDetails(prev => [...prev, `❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`]);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <Card className="max-w-md w-full p-6 max-h-[80vh] overflow-y-auto">
        <div className="text-center mb-4">
          <h2 className="text-xl font-bold mb-2">Database Status</h2>
          
          {status === 'testing' && (
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          )}
          
          {status === 'success' && (
            <div className="text-green-600 text-2xl mb-2">✅</div>
          )}
          
          {status === 'error' && (
            <div className="text-red-600 text-2xl mb-2">❌</div>
          )}
          
          <p className={`text-sm ${
            status === 'success' ? 'text-green-600' : 
            status === 'error' ? 'text-red-600' : 
            'text-muted-foreground'
          }`}>
            {message}
          </p>
        </div>

        <div className="space-y-1 mb-4 text-xs font-mono bg-muted p-3 rounded max-h-60 overflow-y-auto">
          {details.map((detail, index) => (
            <div key={index}>{detail}</div>
          ))}
        </div>

        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={testConnection}
            disabled={status === 'testing'}
            className="flex-1"
          >
            Test Again
          </Button>
          {onClose && (
            <Button onClick={onClose} className="flex-1">
              Close
            </Button>
          )}
        </div>
      </Card>
    </div>
  );
}
