import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface AssessmentSession {
  id?: string;
  instrument: string;
  status: string;
  user_id: string;
  started_at: string;
  completed_at?: string | null;
  score_total?: number | null;
  score_breakdown?: any;
  created_at?: string;
  updated_at?: string;
}

export interface AssessmentAnswer {
  session_id: string;
  question_id: string;
  value: string;
}

export function useAssessmentSession(instrument: string) {
  const [session, setSession] = useState<AssessmentSession | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState<Record<string, any>>({});
  const { toast } = useToast();

  // Test database connection
  const testConnection = async () => {
    try {
      const { error } = await supabase
        .from('assessment_sessions')
        .select('id')
        .limit(1);

      if (error) {
        console.error('Database connection test failed:', error);
        return false;
      }
      return true;
    } catch (error) {
      console.error('Database connection test error:', error);
      return false;
    }
  };

  // Generate a UUID v4
  const generateUUID = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  };

  // Get or create a persistent user ID (works for both authenticated and anonymous users)
  const getOrSetUserId = async () => {
    // First, check if user is authenticated
    const { data: { user } } = await supabase.auth.getUser();

    if (user) {
      // Use authenticated user's ID
      // If there was an anonymous user ID, we could migrate data here in the future
      const anonymousUserId = localStorage.getItem('assessment_user_id');
      if (anonymousUserId) {
        // TODO: Implement data migration from anonymous to authenticated user
        // For now, we'll just clear the anonymous ID
        localStorage.removeItem('assessment_user_id');
      }
      return user.id;
    }

    // For anonymous users, use localStorage-based UUID
    let userId = localStorage.getItem('assessment_user_id');
    if (!userId) {
      userId = generateUUID();
      localStorage.setItem('assessment_user_id', userId);
    }
    return userId;
  };

  // Create new assessment session
  const createSession = async () => {
    setIsLoading(true);
    try {
      const userId = await getOrSetUserId();

      // Test database connection first
      const { error: connectionError } = await supabase
        .from('assessment_sessions')
        .select('id')
        .limit(1);

      if (connectionError) {
        throw new Error(`Koneksi database gagal: ${connectionError.message}`);
      }

      const { data, error } = await supabase
        .from('assessment_sessions')
        .insert({
          instrument,
          status: 'in_progress',
          user_id: userId,
          started_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) {
        if (error.code === 'PGRST301') {
          throw new Error('Tidak dapat mengakses database. Periksa koneksi internet Anda.');
        } else if (error.message.includes('row-level security')) {
          throw new Error('Akses database dibatasi. Silakan hubungi administrator.');
        } else {
          throw new Error(`Gagal membuat sesi: ${error.message}`);
        }
      }

      setSession(data);
      return data;
    } catch (error) {
      console.error('Error creating session:', error);
      const errorMessage = error instanceof Error ? error.message : 'Gagal memulai assessment. Silakan coba lagi.';
      toast({
        title: "Kesalahan",
        description: errorMessage,
        variant: "destructive"
      });
      throw error; // Re-throw to allow caller to handle
    } finally {
      setIsLoading(false);
    }
  };

  // Save answer for current question
  const saveAnswer = async (questionId: string, value: string | number) => {
    if (!session) return;

    try {
      const { error } = await supabase
        .from('assessment_answers')
        .upsert({
          session_id: session.id!,
          question_id: questionId,
          value: value.toString(),
        }, {
          onConflict: 'session_id,question_id'
        });

      if (error) throw error;

      // Update local progress
      setProgress(prev => ({
        ...prev,
        [questionId]: value
      }));

    } catch (error) {
      console.error('Error saving answer:', error);
      toast({
        title: "Kesalahan",
        description: "Gagal menyimpan progress. Silakan coba lagi.",
        variant: "destructive"
      });
    }
  };

  // Complete assessment session
  const completeSession = async (totalScore: number, scoreBreakdown?: any) => {
    if (!session) return;

    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('assessment_sessions')
        .update({
          status: 'completed',
          completed_at: new Date().toISOString(),
          score_total: totalScore,
          score_breakdown: scoreBreakdown,
        })
        .eq('id', session.id!)
        .select()
        .single();

      if (error) throw error;

      setSession(data);
      toast({
        title: "Assessment Selesai",
        description: "Hasil assessment telah tersimpan.",
        variant: "default"
      });
      
      return data;
    } catch (error) {
      console.error('Error completing session:', error);
      toast({
        title: "Kesalahan",
        description: "Gagal menyimpan hasil assessment.",
        variant: "destructive"
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Load existing session progress
  const loadProgress = async (sessionId: string) => {
    try {
      const { data, error } = await supabase
        .from('assessment_answers')
        .select('*')
        .eq('session_id', sessionId);

      if (error) throw error;

      const progressMap = data.reduce((acc, answer) => {
        acc[answer.question_id] = answer.value;
        return acc;
      }, {} as Record<string, any>);

      setProgress(progressMap);
      return progressMap;
    } catch (error) {
      console.error('Error loading progress:', error);
      return {};
    }
  };

  // Load latest 'in_progress' session for the user
  const loadLatestSession = async () => {
    setIsLoading(true);
    try {
      const userId = await getOrSetUserId();

      // Test database connection first
      const { error: connectionError } = await supabase
        .from('assessment_sessions')
        .select('id')
        .limit(1);

      if (connectionError) {
        throw new Error(`Koneksi database gagal: ${connectionError.message}`);
      }

      const { data, error } = await supabase
        .from('assessment_sessions')
        .select('*')
        .eq('user_id', userId)
        .eq('instrument', instrument)
        .eq('status', 'in_progress')
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') { // Ignore 'single row not found' error
        if (error.code === 'PGRST301') {
          throw new Error('Tidak dapat mengakses database. Periksa koneksi internet Anda.');
        } else if (error.message.includes('row-level security')) {
          throw new Error('Akses database dibatasi. Silakan hubungi administrator.');
        } else {
          throw new Error(`Gagal memuat sesi: ${error.message}`);
        }
      }

      if (data) {
        setSession(data);
        await loadProgress(data.id!);
        toast({
          title: "Progress Ditemukan",
          description: "Melanjutkan sesi assessment sebelumnya.",
        });
        return data;
      }
      return null;
    } catch (error) {
      console.error('Error loading latest session:', error);
      const errorMessage = error instanceof Error ? error.message : 'Gagal memuat progress terakhir. Memulai sesi baru.';
      toast({
        title: "Kesalahan",
        description: errorMessage,
        variant: "destructive"
      });
      throw error; // Re-throw to allow caller to handle
    } finally {
      setIsLoading(false);
    }
  };

  return {
    session,
    progress,
    isLoading,
    createSession,
    saveAnswer,
    completeSession,
    loadProgress,
    loadLatestSession,
    testConnection,
  };
}