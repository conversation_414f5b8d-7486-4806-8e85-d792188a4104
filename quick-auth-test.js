#!/usr/bin/env node

/**
 * Quick Authentication Test
 * Tests the basic auth functionality
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.VITE_SUPABASE_ANON_KEY;

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

console.log('🔐 Quick Authentication Test\n');

async function testAuthEndpoints() {
  console.log('Testing authentication endpoints...\n');
  
  // Test 1: Check if we can get current session (should be null for new session)
  console.log('1. Testing session check...');
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    if (error) {
      console.log('❌ Session check failed:', error.message);
      return false;
    }
    console.log('✅ Session check works (current session:', session ? 'authenticated' : 'anonymous', ')');
  } catch (error) {
    console.log('❌ Session check error:', error.message);
    return false;
  }

  // Test 2: Test sign up (with a test email)
  console.log('\n2. Testing sign up functionality...');
  const testEmail = `test-${Date.now()}@example.com`;
  const testPassword = 'testpassword123';
  
  try {
    const { data, error } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          full_name: 'Test User'
        }
      }
    });
    
    if (error) {
      console.log('⚠️  Sign up response:', error.message);
      // This might fail due to email confirmation requirements, which is normal
    } else {
      console.log('✅ Sign up works - user created:', data.user?.email);
    }
  } catch (error) {
    console.log('⚠️  Sign up test:', error.message);
  }

  // Test 3: Test password reset
  console.log('\n3. Testing password reset...');
  try {
    const { error } = await supabase.auth.resetPasswordForEmail(testEmail, {
      redirectTo: 'http://127.0.0.1:8081/change-password'
    });
    
    if (error) {
      console.log('⚠️  Password reset:', error.message);
    } else {
      console.log('✅ Password reset functionality works');
    }
  } catch (error) {
    console.log('⚠️  Password reset test:', error.message);
  }

  return true;
}

async function testUIRoutes() {
  console.log('\n4. Testing UI routes...');
  
  const routes = [
    { path: '/', description: 'Home page' },
    { path: '/login', description: 'Login page' },
    { path: '/register', description: 'Registration page' },
    { path: '/forgot-password', description: 'Forgot password page' },
    { path: '/change-password', description: 'Change password page' },
    { path: '/profile', description: 'Profile page' },
    { path: '/assessment', description: 'Assessment page' }
  ];

  console.log('✅ Available routes:');
  routes.forEach(route => {
    console.log(`   ${route.path} - ${route.description}`);
  });

  return true;
}

async function main() {
  console.log('🌐 Application URL: http://127.0.0.1:8081/\n');
  
  await testAuthEndpoints();
  await testUIRoutes();
  
  console.log('\n' + '='.repeat(50));
  console.log('🎯 MANUAL TESTING INSTRUCTIONS:');
  console.log('');
  console.log('1. 📱 Open: http://127.0.0.1:8081/');
  console.log('   • Check if you see login icon in header');
  console.log('   • Notice generic welcome message');
  console.log('');
  console.log('2. 📝 Test Registration:');
  console.log('   • Go to /register or click login icon → "Daftar sekarang"');
  console.log('   • Fill form with test data');
  console.log('   • Should redirect to login page');
  console.log('');
  console.log('3. 🔑 Test Login:');
  console.log('   • Go to /login');
  console.log('   • Use the credentials you just created');
  console.log('   • Should redirect to home with personalized message');
  console.log('');
  console.log('4. 👤 Test Authenticated Experience:');
  console.log('   • Header should show user icon instead of login icon');
  console.log('   • Click user icon to see dropdown menu');
  console.log('   • Go to /profile to see user information');
  console.log('   • Try taking an assessment');
  console.log('');
  console.log('5. 🚪 Test Logout:');
  console.log('   • Click user icon → "Logout" OR');
  console.log('   • Go to profile page → "Keluar"');
  console.log('   • Should return to anonymous state');
  console.log('');
  console.log('6. 🔄 Test Session Persistence:');
  console.log('   • Login again');
  console.log('   • Refresh the page');
  console.log('   • Should remain logged in');
  console.log('');
  console.log('✨ All features are ready for testing!');
}

main().catch(console.error);
