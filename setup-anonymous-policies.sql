-- Setup RLS Policies for Anonymous Assessment System
-- This script creates permissive RLS policies for anonymous users

-- First, let's disable <PERSON><PERSON> temporarily to clean up
ALTER TABLE assessment_sessions DISABLE ROW LEVEL SECURITY;
ALTER TABLE assessment_answers DISABLE ROW LEVEL SECURITY;

-- Drop all existing policies
DROP POLICY IF EXISTS "Allow anonymous users to create assessment sessions" ON assessment_sessions;
DROP POLICY IF EXISTS "Allow users to read their own assessment sessions" ON assessment_sessions;
DROP POLICY IF EXISTS "Allow users to update their own assessment sessions" ON assessment_sessions;
DROP POLICY IF EXISTS "Allow anonymous users to create assessment answers" ON assessment_answers;
DROP POLICY IF EXISTS "Allow users to read their own assessment answers" ON assessment_answers;
DROP POLICY IF EXISTS "Allow users to update their own assessment answers" ON assessment_answers;
DROP POLICY IF EXISTS "Enable read access for all users" ON assessment_sessions;
DROP POLICY IF EXISTS "Enable insert for all users" ON assessment_sessions;
DROP POLICY IF EXISTS "Enable update for all users" ON assessment_sessions;
DROP POLICY IF EXISTS "Enable read access for all users" ON assessment_answers;
DROP POLICY IF EXISTS "Enable insert for all users" ON assessment_answers;
DROP POLICY IF EXISTS "Enable update for all users" ON assessment_answers;

-- Create very permissive policies for assessment system
-- Assessment Sessions - Allow all operations for all users (including anonymous)
CREATE POLICY "Enable read access for all users" ON assessment_sessions
    FOR SELECT USING (true);

CREATE POLICY "Enable insert for all users" ON assessment_sessions
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Enable update for all users" ON assessment_sessions
    FOR UPDATE USING (true) WITH CHECK (true);

-- Assessment Answers - Allow all operations for all users (including anonymous)
CREATE POLICY "Enable read access for all users" ON assessment_answers
    FOR SELECT USING (true);

CREATE POLICY "Enable insert for all users" ON assessment_answers
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Enable update for all users" ON assessment_answers
    FOR UPDATE USING (true) WITH CHECK (true);

-- Re-enable RLS
ALTER TABLE assessment_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE assessment_answers ENABLE ROW LEVEL SECURITY;

-- Grant permissions to anonymous role
GRANT USAGE ON SCHEMA public TO anon;
GRANT ALL ON assessment_sessions TO anon;
GRANT ALL ON assessment_answers TO anon;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO anon;

-- Grant permissions to authenticated role as well
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON assessment_sessions TO authenticated;
GRANT ALL ON assessment_answers TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- Create helpful indexes
CREATE INDEX IF NOT EXISTS idx_assessment_sessions_user_id ON assessment_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_assessment_sessions_instrument ON assessment_sessions(instrument);
CREATE INDEX IF NOT EXISTS idx_assessment_sessions_status ON assessment_sessions(status);
CREATE INDEX IF NOT EXISTS idx_assessment_sessions_created_at ON assessment_sessions(created_at);
CREATE INDEX IF NOT EXISTS idx_assessment_answers_session_id ON assessment_answers(session_id);
CREATE INDEX IF NOT EXISTS idx_assessment_answers_question_id ON assessment_answers(question_id);

-- Verify tables exist and have correct structure
SELECT table_name, column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name IN ('assessment_sessions', 'assessment_answers')
ORDER BY table_name, ordinal_position;

-- Verify policies are created
SELECT schemaname, tablename, policyname, permissive, roles, cmd
FROM pg_policies 
WHERE tablename IN ('assessment_sessions', 'assessment_answers')
ORDER BY tablename, policyname;
